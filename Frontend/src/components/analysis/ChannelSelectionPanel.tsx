import React from "react";
import { Layers } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";

interface ChannelSelectionPanelProps {
  channels: string[];
  selectedChannels: string[];
  onChannelToggle: (channel: string) => void;
  onSelectAll: () => void;
  onClearAll: () => void;
  hfoCountPerChannel?: Record<string, number>;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

export const ChannelSelectionPanel: React.FC<ChannelSelectionPanelProps> = ({
  channels,
  selectedChannels,
  onChannelToggle,
  onSelectAll,
  onClearAll,
  hfoCountPerChannel = {},
  isCollapsed = false,
  onToggleCollapse,
}) => {
  if (isCollapsed) {
    return (
      <Card className="w-12 bg-gray-50 border-r border-gray-200 flex flex-col items-center py-4">
        <Button variant="ghost" size="sm" onClick={onToggleCollapse} className="p-1" title="Expand channel panel">
          <Layers className="h-5 w-5" />
        </Button>
      </Card>
    );
  }

  return (
    <Card className="w-64 bg-gray-50 border-r border-gray-200 flex flex-col h-full">
      {/* Header */}
      <div className="flex-shrink-0 p-3 border-b border-gray-200 flex items-center justify-between">
        <h3 className="text-sm font-semibold text-gray-900 flex items-center gap-2">
          <Layers className="w-4 h-4 text-gray-600" />
          Channel Selection
        </h3>
        {onToggleCollapse && (
          <Button variant="ghost" size="sm" onClick={onToggleCollapse} className="p-1 h-auto" title="Collapse panel">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
            </svg>
          </Button>
        )}
      </div>

      {/* Selection controls */}
      <div className="flex-shrink-0 p-3 border-b border-gray-200 space-y-2">
        <Button onClick={onSelectAll} variant="outline" size="sm" className="w-full text-xs">
          Select All ({channels.length})
        </Button>
        <Button onClick={onClearAll} variant="outline" size="sm" className="w-full text-xs">
          Clear Selection
        </Button>
        <div className="text-xs text-gray-600 text-center mt-2">
          {selectedChannels.length} of {channels.length} selected
        </div>
      </div>

      {/* Channel list */}
      <ScrollArea className="flex-1 min-h-0">
        <div className="p-3 space-y-1">
          {channels.map((channel) => {
            const hfoCount = hfoCountPerChannel[channel] || 0;
            const isSelected = selectedChannels.includes(channel);

            return (
              <label
                key={channel}
                className={`
                  flex items-center gap-2 p-2 rounded cursor-pointer transition-all
                  hover:bg-white ${isSelected ? "bg-white shadow-sm border border-gray-200" : ""}
                `}
              >
                <Checkbox
                  checked={isSelected}
                  onCheckedChange={() => onChannelToggle(channel)}
                  className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                />
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">{channel}</span>
                    {hfoCount > 0 && <span className="text-xs text-blue-600 font-medium">{hfoCount} HFOs</span>}
                  </div>
                  <div
                    className="h-1 mt-1 rounded-full transition-all"
                    style={{
                      background: isSelected
                        ? `linear-gradient(90deg, #3b82f6 ${Math.min(hfoCount * 2, 100)}%, #e5e7eb ${Math.min(hfoCount * 2, 100)}%)`
                        : "#e5e7eb",
                    }}
                  />
                </div>
              </label>
            );
          })}
        </div>
      </ScrollArea>

      {/* Footer with stats */}
      <div className="flex-shrink-0 p-3 border-t border-gray-200 text-xs text-gray-600">
        <div className="space-y-1">
          <div className="flex justify-between">
            <span>Total HFOs:</span>
            <span className="font-semibold">{Object.values(hfoCountPerChannel).reduce((sum, count) => sum + count, 0)}</span>
          </div>
          <div className="flex justify-between">
            <span>Active channels:</span>
            <span className="font-semibold">{Object.keys(hfoCountPerChannel).filter((ch) => hfoCountPerChannel[ch] > 0).length}</span>
          </div>
        </div>
      </div>
    </Card>
  );
};
