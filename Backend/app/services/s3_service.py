
import boto3
from botocore.exceptions import Client<PERSON>rror

from ..config import settings
from ..constants import (
    DEFAULT_EXPIRY_SECONDS,
    EDF_EXTENSION,
    S3_ERROR_NOT_FOUND,
    S3_LIST_OBJECTS_METHOD,
)
from ..logging_config import get_logger
from ..models import FileInfo
from .multipart_service import MultipartService
from .presigned_url_service import PresignedUrlService

logger = get_logger(__name__)


class S3Service:
    def __init__(self):
        from botocore.config import Config

        accelerate = True if settings.s3_transfer_acceleration else False
        cfg = Config(
            signature_version="s3v4",
            s3={
                "use_accelerate_endpoint": accelerate,
                "addressing_style": "virtual",
            },
        )
        self.s3_client = boto3.client(
            "s3",
            region_name=settings.aws_region,
            config=cfg,
        )
        self.bucket_name = settings.s3_bucket_name
        self.url_service = PresignedUrlService(self.s3_client, self.bucket_name)
        self.multipart_service = MultipartService(
            self.s3_client, self.bucket_name, self.url_service
        )

    def generate_download_url(
        self, key: str, expiry: int = DEFAULT_EXPIRY_SECONDS
    ) -> str:
        return self.url_service.generate_download_url(key, expiry)

    def list_files(self, prefix: str | None = None) -> list[FileInfo]:
        try:
            paginator = self.s3_client.get_paginator(S3_LIST_OBJECTS_METHOD)
            page_iterator = paginator.paginate(
                Bucket=self.bucket_name, Prefix=prefix or ""
            )

            files = []
            for page in page_iterator:
                if "Contents" in page:
                    for obj in page["Contents"]:
                        if obj["Key"].lower().endswith(EDF_EXTENSION):
                            files.append(
                                FileInfo(
                                    key=obj["Key"],
                                    filename=obj["Key"].split("/")[-1],
                                    size=obj["Size"],
                                    last_modified=obj["LastModified"],
                                    etag=obj.get("ETag", "").strip('"'),
                                )
                            )

            return files
        except ClientError as e:
            logger.error(f"Error listing files: {e}")
            raise

    def delete_file(self, key: str) -> bool:
        try:
            self.s3_client.delete_object(Bucket=self.bucket_name, Key=key)
            return True
        except ClientError as e:
            logger.error(f"Error deleting file {key}: {e}")
            raise

    def get_file_metadata(self, key: str) -> dict | None:
        try:
            response = self.s3_client.head_object(Bucket=self.bucket_name, Key=key)
            return {
                "size": response["ContentLength"],
                "last_modified": response["LastModified"],
                "etag": response.get("ETag", "").strip('"'),
                "metadata": response.get("Metadata", {}),
            }
        except ClientError as e:
            if e.response["Error"]["Code"] == S3_ERROR_NOT_FOUND:
                return None
            logger.error(f"Error getting file metadata: {e}")
            raise

    def get_file(self, key: str) -> bytes | None:
        """Download file content from S3"""
        try:
            response = self.s3_client.get_object(Bucket=self.bucket_name, Key=key)
            return response["Body"].read()
        except ClientError as e:
            if e.response["Error"]["Code"] == S3_ERROR_NOT_FOUND:
                return None
            logger.error(f"Error downloading file {key}: {e}")
            raise

    def get_file_range(self, key: str, start: int = 0, end: int | None = None) -> bytes | None:
        """Download partial file content from S3 using byte-range request

        Args:
            key: S3 object key
            start: Start byte position (inclusive)
            end: End byte position (inclusive). If None, reads from start to end of file

        Returns:
            Bytes of the requested range, or None if file not found
        """
        try:
            if end is not None:
                range_header = f"bytes={start}-{end}"
            else:
                range_header = f"bytes={start}-"

            response = self.s3_client.get_object(
                Bucket=self.bucket_name,
                Key=key,
                Range=range_header
            )
            return response["Body"].read()
        except ClientError as e:
            if e.response["Error"]["Code"] == S3_ERROR_NOT_FOUND:
                return None
            logger.error(f"Error downloading file range {key} [{start}:{end}]: {e}")
            raise

    def initiate_multipart_upload(self, key: str, filename: str) -> str:
        return self.multipart_service.initiate_multipart_upload(key, filename)

    def generate_part_url(self, key: str, upload_id: str, part_number: int) -> str:
        return self.multipart_service.generate_part_url(key, upload_id, part_number)

    def complete_multipart_upload(self, key: str, upload_id: str, parts: list[dict]):
        return self.multipart_service.complete_multipart_upload(key, upload_id, parts)

    def abort_multipart_upload(self, key: str, upload_id: str):
        return self.multipart_service.abort_multipart_upload(key, upload_id)


s3_service = S3Service()

